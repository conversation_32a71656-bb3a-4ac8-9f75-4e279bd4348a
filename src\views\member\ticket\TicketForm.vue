<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="65%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="工单号" prop="no">
        <el-input v-model="formData.no" placeholder="请输入工单号" />
      </el-form-item>
      <el-form-item label="工单标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入工单标题" />
      </el-form-item>
      <el-form-item label="用户编号" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户编号" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TICKET_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="formData.priority" placeholder="请选择优先级">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TICKET_PRIORITY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="附件URL列表" prop="attachmentUrls">
        <el-input v-model="formData.attachmentUrls" placeholder="请输入附件URL列表" />
      </el-form-item>
      <el-form-item label="问题描述" prop="description">
        <el-input
          v-model="formData.description"
          placeholder="请输入描述"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="管理员编号" prop="adminId">
        <el-input v-model="formData.adminId" placeholder="请输入管理员编号" />
      </el-form-item>
      <el-form-item label="用户评分" prop="rating">
        <el-input v-model="formData.rating" placeholder="请输入用户评分" />
      </el-form-item>
      <el-form-item label="评分备注" prop="ratingComment">
        <el-input v-model="formData.ratingComment" placeholder="请输入评分备注" />
      </el-form-item>
      <el-form-item label="关闭时间" prop="closedTime">
        <el-date-picker
          v-model="formData.closedTime"
          type="date"
          value-format="x"
          placeholder="选择关闭时间"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.TICKET_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="用户工单信息" name="ticketMessage">
        <TicketMessageForm ref="ticketMessageFormRef" :ticket-id="formData.id" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TicketApi, TicketVO } from '@/api/member/ticket'
import TicketMessageForm from './components/TicketMessageForm.vue'

/** 用户工单 表单 */
defineOptions({ name: 'TicketForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  no: undefined,
  title: undefined,
  userId: undefined,
  type: undefined,
  priority: undefined,
  attachmentUrls: undefined,
  description: undefined,
  adminId: undefined,
  rating: undefined,
  ratingComment: undefined,
  closedTime: undefined,
  status: undefined
})
const formRules = reactive({
  no: [{ required: true, message: '工单号不能为空', trigger: 'blur' }],
  title: [{ required: true, message: '工单标题不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '用户编号不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
  priority: [{ required: true, message: '优先级不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('ticketMessage')
const ticketMessageFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TicketApi.getTicket(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await ticketMessageFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'ticketMessage'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TicketVO
    // 拼接子表的数据
    data.ticketMessages = ticketMessageFormRef.value.getData()
    if (formType.value === 'create') {
      await TicketApi.createTicket(data)
      message.success(t('common.createSuccess'))
    } else {
      await TicketApi.updateTicket(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    no: undefined,
    title: undefined,
    userId: undefined,
    type: undefined,
    priority: undefined,
    attachmentUrls: undefined,
    description: undefined,
    adminId: undefined,
    rating: undefined,
    ratingComment: undefined,
    closedTime: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>