<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="90%" :close-on-click-modal="false">
    <div class="ticket-container" v-loading="formLoading">
      <!-- 工单信息区域 -->
      <div class="ticket-header">
        <el-card class="ticket-info-card">
          <template #header>
            <div class="card-header">
              <span class="ticket-title">{{ formData.title || '工单详情' }}</span>
              <div class="ticket-status">
                <el-tag
                  :type="getStatusTagType(formData.status)"
                  size="large"
                  effect="dark"
                >
                  {{ getStatusText(formData.status) }}
                </el-tag>
                <el-tag
                  :type="getPriorityTagType(formData.priority)"
                  size="small"
                  class="ml-2"
                >
                  {{ getPriorityText(formData.priority) }}
                </el-tag>
              </div>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>工单号：</label>
                <span>{{ formData.no }}</span>
              </div>
              <div class="info-item">
                <label>用户编号：</label>
                <span>{{ formData.userId }}</span>
              </div>
              <div class="info-item">
                <label>工单类型：</label>
                <span>{{ getTypeText(formData.type) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatTime(formData.createTime) }}</span>
              </div>
              <div class="info-item">
                <label>管理员：</label>
                <span>{{ formData.adminId || '未分配' }}</span>
              </div>
              <div class="info-item" v-if="formData.rating">
                <label>用户评分：</label>
                <el-rate v-model="formData.rating" disabled show-score />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>问题描述：</label>
                <div class="description-text">{{ formData.description }}</div>
              </div>
              <div class="info-item" v-if="formData.attachmentUrls && formData.attachmentUrls.length > 0">
                <label>附件：</label>
                <div class="attachment-container">
                  <UploadImgs
                    v-model="formData.attachmentUrls"
                    :disabled="true"
                    :limit="10"
                    height="60px"
                    width="60px"
                  />
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 消息对话区域 -->
      <div class="ticket-messages">
        <el-card class="messages-card">
          <template #header>
            <div class="card-header">
              <span>工单对话</span>
              <el-button type="primary" size="small" @click="scrollToBottom">
                <Icon icon="ep:bottom" />
                滚动到底部
              </el-button>
            </div>
          </template>

          <div class="messages-container" ref="messagesContainer">
            <div
              v-for="(message, index) in ticketMessages"
              :key="message.id || index"
              class="message-item"
              :class="{ 'admin-message': message.messageType === 2, 'user-message': message.messageType === 1 }"
            >
              <div class="message-avatar">
                <el-avatar
                  :size="40"
                  :src="message.messageType === 2 ? '/admin-avatar.png' : '/user-avatar.png'"
                >
                  {{ message.messageType === 2 ? '客服' : '用户' }}
                </el-avatar>
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="sender-name">{{ message.replierName || (message.messageType === 2 ? '客服' : '用户') }}</span>
                  <span class="message-time">{{ formatTime(message.createTime) }}</span>
                </div>
                <div class="message-body">
                  <div class="message-text">{{ message.content }}</div>
                  <div v-if="message.attachmentUrls && message.attachmentUrls.length > 0" class="message-attachments">
                    <UploadImgs
                      v-model="message.attachmentUrls"
                      :disabled="true"
                      :limit="10"
                      height="80px"
                      width="80px"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!ticketMessages || ticketMessages.length === 0" class="empty-messages">
              <el-empty description="暂无对话记录" />
            </div>
          </div>
        </el-card>
      </div>

      <!-- 操作区域 -->
      <div class="ticket-actions">
        <el-card class="actions-card">
          <template #header>
            <span>工单操作</span>
          </template>

          <el-row :gutter="20">
            <!-- 状态操作 -->
            <el-col :span="12">
              <div class="action-section">
                <h4>状态管理</h4>
                <el-form :model="statusForm" label-width="80px">
                  <el-form-item label="当前状态">
                    <el-select v-model="statusForm.status" placeholder="请选择状态" @change="handleStatusChange">
                      <el-option
                        v-for="dict in getIntDictOptions(DICT_TYPE.TICKET_STATUS)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="分配给">
                    <el-input v-model="statusForm.adminId" placeholder="管理员编号" />
                  </el-form-item>
                </el-form>
              </div>
            </el-col>

            <!-- 快速回复 -->
            <el-col :span="12">
              <div class="action-section">
                <h4>快速回复</h4>
                <el-form :model="replyForm" label-width="80px">
                  <el-form-item label="回复模板">
                    <el-select v-model="replyForm.template" placeholder="选择模板" @change="handleTemplateChange">
                      <el-option label="问题已收到，正在处理中" value="received" />
                      <el-option label="需要更多信息" value="need_info" />
                      <el-option label="问题已解决" value="resolved" />
                      <el-option label="自定义回复" value="custom" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="回复内容">
                    <el-input
                      v-model="replyForm.content"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入回复内容"
                    />
                  </el-form-item>
                  <el-form-item label="附件">
                    <UploadImgs
                      v-model="replyForm.attachmentUrls"
                      :limit="5"
                      height="60px"
                      width="60px"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleReply" :loading="replyLoading">
                      发送回复
                    </el-button>
                    <el-button @click="resetReplyForm">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TicketApi, TicketVO } from '@/api/member/ticket'
import TicketMessageForm from './components/TicketMessageForm.vue'

/** 用户工单 表单 */
defineOptions({ name: 'TicketForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  no: undefined,
  title: undefined,
  userId: undefined,
  type: undefined,
  priority: undefined,
  attachmentUrls: undefined,
  description: undefined,
  adminId: undefined,
  rating: undefined,
  ratingComment: undefined,
  closedTime: undefined,
  status: undefined
})
const formRules = reactive({
  no: [{ required: true, message: '工单号不能为空', trigger: 'blur' }],
  title: [{ required: true, message: '工单标题不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '用户编号不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
  priority: [{ required: true, message: '优先级不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('ticketMessage')
const ticketMessageFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TicketApi.getTicket(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await ticketMessageFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'ticketMessage'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TicketVO
    // 拼接子表的数据
    data.ticketMessages = ticketMessageFormRef.value.getData()
    if (formType.value === 'create') {
      await TicketApi.createTicket(data)
      message.success(t('common.createSuccess'))
    } else {
      await TicketApi.updateTicket(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    no: undefined,
    title: undefined,
    userId: undefined,
    type: undefined,
    priority: undefined,
    attachmentUrls: undefined,
    description: undefined,
    adminId: undefined,
    rating: undefined,
    ratingComment: undefined,
    closedTime: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>