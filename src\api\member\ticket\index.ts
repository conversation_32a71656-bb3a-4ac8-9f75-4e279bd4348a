import request from '@/config/axios'

// 用户工单 VO
export interface TicketVO {
  id: number // 编号
  no: string // 工单号
  title: string // 工单标题
  userId: number // 用户编号
  type: number // 类型
  priority: number // 优先级
  attachmentUrls: string // 附件URL列表
  description: string // 问题描述
  adminId: number // 管理员编号
  rating: number // 用户评分;1-5分
  ratingComment: string // 评分备注
  closedTime: Date // 关闭时间
  status: number // 状态
}

// 用户工单 API
export const TicketApi = {
  // 查询用户工单分页
  getTicketPage: async (params: any) => {
    return await request.get({ url: `/member/ticket/page`, params })
  },

  // 查询用户工单详情
  getTicket: async (id: number) => {
    return await request.get({ url: `/member/ticket/get?id=` + id })
  },

  // 新增用户工单
  createTicket: async (data: TicketVO) => {
    return await request.post({ url: `/member/ticket/create`, data })
  },

  // 修改用户工单
  updateTicket: async (data: TicketVO) => {
    return await request.put({ url: `/member/ticket/update`, data })
  },

  // 删除用户工单
  deleteTicket: async (id: number) => {
    return await request.delete({ url: `/member/ticket/delete?id=` + id })
  },

  // 导出用户工单 Excel
  exportTicket: async (params) => {
    return await request.download({ url: `/member/ticket/export-excel`, params })
  },

// ==================== 子表（用户工单信息） ====================

  // 获得用户工单信息列表
  getTicketMessageListByTicketId: async (ticketId) => {
    return await request.get({ url: `/member/ticket/ticket-message/list-by-ticket-id?ticketId=` + ticketId })
  }
}